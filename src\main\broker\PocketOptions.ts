import { io, Socket } from 'socket.io-client'

export class PocketOptions {
  private static instance: PocketOptions
  private socket: Socket | null = null

  constructor(
    private ssID: string,
    private isDemo: boolean
  ) {}

  static getInstance(ssID: string, isDemo: boolean): PocketOptions {
    if (!PocketOptions.instance) {
      PocketOptions.instance = new PocketOptions(ssID, isDemo)
    }
    return PocketOptions.instance
  }

  async connect(): Promise<void> {
    if (this.socket && this.socket.connected) return

    try {
    } catch (error) {}
  }
}
