@import './base.css';
@import 'tailwindcss';

/* Trading Platform Global Styles */
:root {
  /* Trading Platform Color Scheme */
  --trading-bg-primary: #0a0b0d;
  --trading-bg-secondary: #1a1b1e;
  --trading-bg-tertiary: #2a2b2e;
  --trading-text-primary: #ffffff;
  --trading-text-secondary: #b0b3b8;
  --trading-text-muted: #65676b;
  --trading-accent-green: #00d4aa;
  --trading-accent-red: #ff4757;
  --trading-accent-blue: #3742fa;
  --trading-border: #3e4042;
  --trading-shadow: rgba(0, 0, 0, 0.3);
}

body {
  @apply bg-gray-900 text-white font-sans antialiased;
  margin: 0;
  padding: 0;
  overflow: hidden;
  user-select: none;
  background: var(--trading-bg-primary);
  color: var(--trading-text-primary);
}

#root {
  @apply w-full h-screen flex flex-col;
}

/* Trading Platform Specific Utilities */
.trading-card {
  @apply bg-gray-800 border border-gray-700 rounded-lg shadow-lg;
  background: var(--trading-bg-secondary);
  border-color: var(--trading-border);
}

.trading-button-primary {
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  background: var(--trading-accent-blue);
  color: var(--trading-text-primary);
}

.trading-button-primary:hover {
  @apply transform scale-105;
  filter: brightness(1.1);
}

.trading-button-success {
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  background: var(--trading-accent-green);
  color: var(--trading-text-primary);
}

.trading-button-danger {
  @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  background: var(--trading-accent-red);
  color: var(--trading-text-primary);
}

.trading-input {
  @apply px-3 py-2 rounded-md border focus:outline-none focus:ring-2 transition-all duration-200;
  background: var(--trading-bg-tertiary);
  border-color: var(--trading-border);
  color: var(--trading-text-primary);
}

.trading-input:focus {
  border-color: var(--trading-accent-blue);
  box-shadow: 0 0 0 2px rgba(55, 66, 250, 0.2);
}

/* Price Display Utilities */
.price-up {
  color: var(--trading-accent-green);
}

.price-down {
  color: var(--trading-accent-red);
}

.price-neutral {
  color: var(--trading-text-secondary);
}

/* Animation Utilities */
.fade-in {
  @apply animate-fade-in;
}

.slide-up {
  @apply animate-slide-up;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--trading-bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--trading-border);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--trading-text-muted);
}
