import { logger, Logger, LogLevel } from './logger'

// Demo function to showcase the logger functionality
export function demonstrateLogger(): void {
  console.log('\n=== Logger Demo ===\n')

  // Test all log levels
  logger.debug('This is a debug message with some data', { userId: 123, action: 'login' })
  logger.info('Application started successfully')
  logger.warn('This is a warning about deprecated API usage')
  logger.error('An error occurred while processing request', new Error('Sample error'))
  logger.fatal('Critical system failure detected')

  console.log('\n=== Testing Log Level Filtering ===\n')

  // Test log level filtering
  Logger.setLogLevel(LogLevel.WARN)
  console.log('Log level set to WARN - only WARN, ERROR, and FATAL should appear:')

  logger.debug('This debug message should NOT appear')
  logger.info('This info message should NOT appear')
  logger.warn('This warning should appear')
  logger.error('This error should appear')
  logger.fatal('This fatal should appear')

  // Reset log level
  Logger.setLogLevel(LogLevel.DEBUG)
  console.log('\nLog level reset to DEBUG - all messages should appear again')
}

// Example usage from different module contexts
export class SampleService {
  constructor() {
    logger.info('SampleService initialized')
  }

  processData(data: unknown): void {
    logger.debug('Processing data', { dataSize: JSON.stringify(data).length })

    try {
      // Simulate some processing
      if (!data) {
        throw new Error('No data provided')
      }
      logger.info('Data processed successfully')
    } catch (error) {
      logger.error('Failed to process data', error)
    }
  }
}
