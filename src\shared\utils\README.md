# Universal Logger Utility

A universal logger utility that uses `picocolors` for colored console output and automatically identifies the source file/module.

## Features

- 🎨 **Colored Output**: Uses `picocolors` for beautiful colored console output
- 📍 **Auto Module Detection**: Automatically identifies the calling file and extracts module category
- ⏰ **Timestamps**: ISO format timestamps for all log entries
- 🎯 **Log Levels**: Support for DEBUG, INFO, WARN, ERROR, and FATAL levels
- 🚀 **Easy Import**: Simple import and usage across the entire codebase
- 🔧 **Configurable**: Adjustable log levels for filtering

## Log Format

```
[timestamp] 🔧 LEVEL [ModuleCategory] Message
```

Example output:
```
[2024-01-15 14:30:25] 🔧 INFO [Broker] Connection established successfully
[2024-01-15 14:30:26] ⚠️ WARN [Renderer] Deprecated API usage detected
[2024-01-15 14:30:27] ❌ ERROR [Main] Failed to load configuration file
```

## Usage

### Basic Import and Usage

```typescript
import { logger } from '../../shared/utils'

// Simple logging
logger.info('Application started')
logger.warn('This is a warning')
logger.error('An error occurred', error)

// With additional data
logger.debug('User action', { userId: 123, action: 'login' })
```

### Alternative Import (Class-based)

```typescript
import { Logger, LogLevel } from '../../shared/utils'

Logger.info('Using class-based approach')
Logger.setLogLevel(LogLevel.WARN) // Only show WARN and above
```

## Log Levels

| Level | Emoji | Color | Description |
|-------|-------|-------|-------------|
| DEBUG | 🔍 | Gray | Detailed debugging information |
| INFO  | 🔧 | Blue | General information messages |
| WARN  | ⚠️ | Yellow | Warning messages |
| ERROR | ❌ | Red | Error messages |
| FATAL | 💀 | Magenta | Critical system failures |

## Module Categories

The logger automatically detects module categories based on file paths:

- **Broker**: Files in `src/main/broker/`
- **Renderer**: Files in `src/renderer/`
- **Main**: Files in `src/main/`
- **Preload**: Files in `src/preload/`
- **Shared**: Files in `src/shared/`
- **Custom**: First directory name (capitalized) for other paths

## Configuration

### Setting Log Level

```typescript
import { Logger, LogLevel } from '../../shared/utils'

// Only show warnings and errors
Logger.setLogLevel(LogLevel.WARN)

// Show all messages (default)
Logger.setLogLevel(LogLevel.DEBUG)
```

## Examples

### In a Broker Class

```typescript
// src/main/broker/PocketOptions.ts
import { logger } from '../../shared/utils'

export class PocketOptions {
  async connect(): Promise<void> {
    logger.info('Attempting to connect to PocketOptions socket')
    
    try {
      // Connection logic
      logger.info('Connection established successfully')
    } catch (error) {
      logger.error('Failed to connect to PocketOptions socket', error)
      throw error
    }
  }
}
```

Output:
```
[2024-01-15 14:30:25] 🔧 INFO [Broker] Attempting to connect to PocketOptions socket
[2024-01-15 14:30:26] 🔧 INFO [Broker] Connection established successfully
```

### In a Renderer Component

```typescript
// src/renderer/src/components/Dashboard.tsx
import { logger } from '../../../shared/utils'

export function Dashboard() {
  useEffect(() => {
    logger.debug('Dashboard component mounted')
    
    // Component logic
    logger.info('Dashboard data loaded successfully')
  }, [])
}
```

Output:
```
[2024-01-15 14:30:25] 🔍 DEBUG [Renderer] Dashboard component mounted
[2024-01-15 14:30:26] 🔧 INFO [Renderer] Dashboard data loaded successfully
```

## Demo

Run the logger demo to see all features in action:

```typescript
import { demonstrateLogger } from '../../shared/utils/logger-demo'

demonstrateLogger()
```

This will show examples of all log levels, module detection, and log level filtering.
