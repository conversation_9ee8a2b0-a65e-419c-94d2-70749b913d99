import { resolve } from 'path'
import { defineConfig, externalizeDepsPlugin } from 'electron-vite'
import react from '@vitejs/plugin-react'

export default defineConfig({
  main: {
    plugins: [externalizeDepsPlugin()]
  },
  preload: {
    plugins: [externalizeDepsPlugin()]
  },
  renderer: {
    resolve: {
      alias: {
        '@renderer': resolve('src/renderer/src'),
        '@shared': resolve('src/shared'),
        '@types': resolve('src/shared/types'),
        '@utils': resolve('src/shared/utils'),
        '@constants': resolve('src/shared/constants'),
        '@enums': resolve('src/shared/enums')
      }
    },
    plugins: [react()],
    css: {
      postcss: './postcss.config.cjs'
    }
  }
})
