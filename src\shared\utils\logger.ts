import pc from 'picocolors'
import path from 'path'

export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  FATAL = 4
}

interface LogConfig {
  level: LogLevel
  emoji: string
  color: (text: string) => string
}

const LOG_CONFIGS: Record<LogLevel, LogConfig> = {
  [LogLevel.DEBUG]: {
    level: LogLevel.DEBUG,
    emoji: '🔍',
    color: pc.gray
  },
  [LogLevel.INFO]: {
    level: LogLevel.INFO,
    emoji: 'ℹ️',
    color: pc.blue
  },
  [LogLevel.WARN]: {
    level: LogLevel.WARN,
    emoji: '⚠️',
    color: pc.yellow
  },
  [LogLevel.ERROR]: {
    level: LogLevel.ERROR,
    emoji: '❌',
    color: pc.red
  },
  [LogLevel.FATAL]: {
    level: LogLevel.FATAL,
    emoji: '💀',
    color: pc.magenta
  }
}

class Logger {
  private static minLogLevel: LogLevel = LogLevel.DEBUG

  static setLogLevel(level: LogLevel): void {
    Logger.minLogLevel = level
  }

  private static getTimestamp(): string {
    const now = new Date()
    return now.toISOString().replace('T', ' ').substring(0, 19)
  }

  private static getModuleCategory(filePath: string): string {
    try {
      // Normalize the path and get relative path from src
      const normalizedPath = path.normalize(filePath)
      const srcIndex = normalizedPath.lastIndexOf('src')

      if (srcIndex === -1) {
        return 'Unknown'
      }

      const relativePath = normalizedPath.substring(srcIndex + 4) // +4 to skip 'src/'
      const pathParts = relativePath.split(path.sep).filter((part) => part.length > 0)

      if (pathParts.length === 0) {
        return 'Root'
      }

      // Extract meaningful category from path structure
      if (pathParts.includes('broker')) {
        return 'Broker'
      } else if (pathParts.includes('renderer')) {
        return 'Renderer'
      } else if (pathParts.includes('main')) {
        return 'Main'
      } else if (pathParts.includes('preload')) {
        return 'Preload'
      } else if (pathParts.includes('shared')) {
        return 'Shared'
      } else {
        // Use the first directory name, capitalized
        return pathParts[0].charAt(0).toUpperCase() + pathParts[0].slice(1)
      }
    } catch {
      return 'Unknown'
    }
  }

  private static getCaller(): string {
    const originalPrepareStackTrace = Error.prepareStackTrace
    Error.prepareStackTrace = (_, stack) => stack

    const err = new Error()
    const stack = err.stack as unknown as NodeJS.CallSite[]

    Error.prepareStackTrace = originalPrepareStackTrace

    // Find the first stack frame that's not from this logger file
    for (let i = 0; i < stack.length; i++) {
      const fileName = stack[i].getFileName()
      if (fileName && !fileName.includes('logger.ts')) {
        return fileName
      }
    }

    return 'Unknown'
  }

  private static log(level: LogLevel, message: string, ...args: unknown[]): void {
    if (level < Logger.minLogLevel) {
      return
    }

    const config = LOG_CONFIGS[level]
    const timestamp = Logger.getTimestamp()
    const callerPath = Logger.getCaller()
    const moduleCategory = Logger.getModuleCategory(callerPath)
    const levelName = LogLevel[level]

    // Format: [timestamp] 🔧 LEVEL [ModuleCategory] Message
    const logPrefix = `[${pc.dim(timestamp)}] ${config.emoji} ${config.color(levelName)} [${pc.cyan(moduleCategory)}]`
    const formattedMessage = `${logPrefix} ${message}`

    // Use appropriate console method based on log level
    switch (level) {
      case LogLevel.DEBUG:
        console.debug(formattedMessage, ...args)
        break
      case LogLevel.INFO:
        console.info(formattedMessage, ...args)
        break
      case LogLevel.WARN:
        console.warn(formattedMessage, ...args)
        break
      case LogLevel.ERROR:
      case LogLevel.FATAL:
        console.error(formattedMessage, ...args)
        break
      default:
        console.log(formattedMessage, ...args)
    }
  }

  static debug(message: string, ...args: unknown[]): void {
    Logger.log(LogLevel.DEBUG, message, ...args)
  }

  static info(message: string, ...args: unknown[]): void {
    Logger.log(LogLevel.INFO, message, ...args)
  }

  static warn(message: string, ...args: unknown[]): void {
    Logger.log(LogLevel.WARN, message, ...args)
  }

  static error(message: string, ...args: unknown[]): void {
    Logger.log(LogLevel.ERROR, message, ...args)
  }

  static fatal(message: string, ...args: unknown[]): void {
    Logger.log(LogLevel.FATAL, message, ...args)
  }
}

// Export both the Logger class and a default instance for convenience
export { Logger }
export default Logger
